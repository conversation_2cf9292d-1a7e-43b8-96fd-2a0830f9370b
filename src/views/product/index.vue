<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" ref="queryForm">
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入产品名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商品编号" prop="offer">
          <el-input
            v-model="queryParams.offer"
            placeholder="请输入商品编号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in stateOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="cate_id">
          <el-select v-model="queryParams.cate_id" placeholder="请选择分类" clearable>
            <el-option
              v-for="dict in categoryOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-container">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增产品
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" align="center" label="序号" />
        <el-table-column prop="name" label="产品名称" align="center" min-width="120" />
        <el-table-column prop="cover" label="产品图片" align="center" width="100">
          <template #default="scope">
            <el-image
              v-if="scope.row.cover"
              :src="scope.row.cover"
              :preview-src-list="[scope.row.cover]"
              style="width: 50px; height: 50px"
              fit="cover"
            />
            <span v-else>暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column prop="offer" label="商品编号" align="center" min-width="120" />
        <el-table-column prop="price" label="面值" align="center" width="100">
          <template #default="scope">
            ¥{{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="discount" label="折扣" align="center" width="80">
          <template #default="scope">
            {{ scope.row.discount }}%
          </template>
        </el-table-column>
        <el-table-column prop="discounted_price" label="折扣后价格" align="center" width="120">
          <template #default="scope">
            ¥{{ scope.row.discounted_price }}
          </template>
        </el-table-column>
        <el-table-column prop="cate_id" label="分类" align="center" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.cate_id === 6 ? 'warning' : 'success'">
              {{ getCategoryName(scope.row.cate_id) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
              {{ scope.row.state === 1 ? '正常' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" align="center" width="160" />
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { getProductList, deleteProduct } from '@/api/product'

export default {
  name: 'ProductList',
  components: {
    Search,
    Refresh,
    Plus
  },
  setup() {
    const router = useRouter()
    
    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      name: '',
      offer: '',
      state: '',
      cate_id: ''
    })
    
    // 状态选项
    const stateOptions = [
      { value: 1, label: '正常' },
      { value: 0, label: '下架' }
    ]
    
    // 分类选项
    const categoryOptions = [
      { value: 6, label: '飞机大厨限时活动' },
      { value: 8, label: '飞机大厨' }
    ]
    
    // 表格数据
    const loading = ref(false)
    const tableData = ref([])
    const total = ref(0)
    
    // 获取分类名称
    const getCategoryName = (cateId) => {
      const category = categoryOptions.find(item => item.value === cateId)
      return category ? category.label : '未知分类'
    }
    
    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      try {
        // 实际项目中应该调用API
        // const response = await getProductList(queryParams)
        // tableData.value = response.data.list
        // total.value = response.data.total
        
        // 模拟数据
        setTimeout(() => {
          const mockData = []
          for (let i = 0; i < queryParams.pageSize; i++) {
            const index = (queryParams.pageNum - 1) * queryParams.pageSize + i + 1
            const price = Math.floor(Math.random() * 1000) + 100
            const discount = Math.floor(Math.random() * 30) + 70
            mockData.push({
              product_id: index,
              name: '产品名称' + index,
              cover: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
              offer: 'PRD' + String(index).padStart(5, '0'),
              price: price,
              discount: discount,
              discounted_price: Math.floor(price * discount / 100),
              cate_id: Math.random() > 0.5 ? 6 : 8,
              state: Math.random() > 0.3 ? 1 : 0,
              create_time: '2024-01-' + String(Math.floor(Math.random() * 30) + 1).padStart(2, '0') + ' 10:30:00'
            })
          }
          tableData.value = mockData
          total.value = 100
          loading.value = false
        }, 500)
      } catch (error) {
        console.error('获取产品列表失败:', error)
        loading.value = false
      }
    }
    
    // 搜索
    const handleQuery = () => {
      queryParams.pageNum = 1
      getTableData()
    }
    
    // 重置
    const resetQuery = () => {
      queryParams.name = ''
      queryParams.offer = ''
      queryParams.state = ''
      queryParams.cate_id = ''
      handleQuery()
    }
    
    // 新增
    const handleAdd = () => {
      router.push('/product/create')
    }
    
    // 编辑
    const handleEdit = (row) => {
      router.push(`/product/edit/${row.product_id}`)
    }
    
    // 查看
    const handleView = (row) => {
      ElMessage({
        message: `查看产品ID为${row.product_id}的数据`,
        type: 'info'
      })
    }
    
    // 删除
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确认删除产品"${row.name}"吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          // 实际项目中应该调用API
          // await deleteProduct(row.product_id)
          
          // 模拟删除
          ElMessage({
            type: 'success',
            message: '删除成功'
          })
          getTableData()
        } catch (error) {
          console.error('删除失败:', error)
        }
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
    
    // 分页大小变化
    const handleSizeChange = (val) => {
      queryParams.pageSize = val
      getTableData()
    }
    
    // 页码变化
    const handleCurrentChange = (val) => {
      queryParams.pageNum = val
      getTableData()
    }
    
    onMounted(() => {
      getTableData()
    })
    
    return {
      queryParams,
      stateOptions,
      categoryOptions,
      loading,
      tableData,
      total,
      getCategoryName,
      handleQuery,
      resetQuery,
      handleAdd,
      handleEdit,
      handleView,
      handleDelete,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
