<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>新增产品</span>
          <el-button @click="goBack">返回</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入产品名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品图片" prop="cover">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="handleUpload"
          >
            <img v-if="form.cover" :src="form.cover" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">支持 jpg、png 格式，大小不超过 2MB</div>
        </el-form-item>

        <el-form-item label="商品编号" prop="offer">
          <el-input
            v-model="form.offer"
            placeholder="请输入商品编号"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="面值" prop="price">
          <el-input-number
            v-model="form.price"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="999999.99"
            placeholder="请输入面值"
            style="width: 100%"
            @change="calculateDiscountedPrice"
          />
        </el-form-item>

        <el-form-item label="折扣" prop="discount">
          <el-input-number
            v-model="form.discount"
            :precision="0"
            :step="1"
            :min="0"
            :max="100"
            placeholder="请输入折扣"
            style="width: 100%"
            @change="calculateDiscountedPrice"
          />
          <span style="margin-left: 10px; color: #909399;">%</span>
        </el-form-item>

        <el-form-item label="折扣后价格" prop="discounted_price">
          <el-input-number
            v-model="form.discounted_price"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="999999.99"
            placeholder="自动计算或手动输入"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="分类" prop="cate_id">
          <el-select v-model="form.cate_id" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { createProduct } from '@/api/product'

export default {
  name: 'ProductCreate',
  components: {
    Plus
  },
  setup() {
    const router = useRouter()
    const formRef = ref(null)
    const loading = ref(false)

    // 表单数据
    const form = reactive({
      name: '',
      cover: '',
      offer: '',
      price: null,
      discount: 100,
      discounted_price: null,
      cate_id: null,
      state: 1
    })

    // 分类选项
    const categoryOptions = [
      { value: 6, label: '飞机大厨限时活动' },
      { value: 8, label: '飞机大厨' }
    ]

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入产品名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
      ],
      offer: [
        { required: true, message: '请输入商品编号', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
      ],
      price: [
        { required: true, message: '请输入面值', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '面值必须大于0', trigger: 'blur' }
      ],
      discount: [
        { required: true, message: '请输入折扣', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '折扣范围为0-100', trigger: 'blur' }
      ],
      cate_id: [
        { required: true, message: '请选择分类', trigger: 'change' }
      ],
      state: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    // 计算折扣后价格
    const calculateDiscountedPrice = () => {
      if (form.price && form.discount !== null) {
        form.discounted_price = Math.round(form.price * form.discount / 100 * 100) / 100
      }
    }

    // 文件上传前验证
    const beforeUpload = (file) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    }

    // 处理文件上传
    const handleUpload = (options) => {
      // 这里应该调用实际的上传API
      // 现在模拟上传成功
      const file = options.file
      const reader = new FileReader()
      reader.onload = (e) => {
        form.cover = e.target.result
        ElMessage.success('图片上传成功')
      }
      reader.readAsDataURL(file)
    }

    // 提交表单
    const submitForm = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            // 实际项目中应该调用API
            // await createProduct(form)
            
            // 模拟提交
            setTimeout(() => {
              ElMessage.success('产品创建成功')
              router.push('/product')
              loading.value = false
            }, 1000)
          } catch (error) {
            console.error('创建产品失败:', error)
            ElMessage.error('创建产品失败')
            loading.value = false
          }
        }
      })
    }

    // 重置表单
    const resetForm = () => {
      formRef.value.resetFields()
      form.cover = ''
    }

    // 返回列表页
    const goBack = () => {
      router.push('/product')
    }

    return {
      formRef,
      form,
      rules,
      categoryOptions,
      loading,
      calculateDiscountedPrice,
      beforeUpload,
      handleUpload,
      submitForm,
      resetForm,
      goBack
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
