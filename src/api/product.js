import request from '@/utils/request'

// 获取产品列表
export function getProductList(params) {
  return request({
    url: '/product/list',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductDetail(id) {
  return request({
    url: `/product/detail/${id}`,
    method: 'get'
  })
}

// 创建产品
export function createProduct(data) {
  return request({
    url: '/product/create',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProduct(id, data) {
  return request({
    url: `/product/update/${id}`,
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/product/delete/${id}`,
    method: 'delete'
  })
}

// 上传产品图片
export function uploadProductImage(data) {
  return request({
    url: '/product/upload-image',
    method: 'post',
    data
  })
}
